package com.raindrop.manga_service.mapper;

import com.raindrop.manga_service.dto.request.GenreRequest;
import com.raindrop.manga_service.dto.response.GenreResponse;
import com.raindrop.manga_service.entity.Genre;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-01T10:48:35+0700",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class GenreMapperImpl implements GenreMapper {

    @Override
    public Genre toGenre(GenreRequest request) {
        if ( request == null ) {
            return null;
        }

        Genre.GenreBuilder genre = Genre.builder();

        genre.description( request.getDescription() );
        genre.name( request.getName() );

        return genre.build();
    }

    @Override
    public GenreResponse toGenreResponse(Genre genre) {
        if ( genre == null ) {
            return null;
        }

        GenreResponse.GenreResponseBuilder genreResponse = GenreResponse.builder();

        genreResponse.id( genre.getId() );
        genreResponse.name( genre.getName() );
        genreResponse.description( genre.getDescription() );

        return genreResponse.build();
    }

    @Override
    public void updateGenre(Genre genre, GenreRequest request) {
        if ( request == null ) {
            return;
        }

        genre.setDescription( request.getDescription() );
        genre.setName( request.getName() );
    }
}
