package com.raindrop.manga_service.mapper;

import com.raindrop.manga_service.dto.request.MangaRequest;
import com.raindrop.manga_service.dto.response.MangaManagementResponse;
import com.raindrop.manga_service.dto.response.MangaResponse;
import com.raindrop.manga_service.dto.response.MangaSummaryResponse;
import com.raindrop.manga_service.entity.Manga;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-01T10:48:35+0700",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MangaMapperImpl implements MangaMapper {

    @Override
    public Manga toManga(MangaRequest request) {
        if ( request == null ) {
            return null;
        }

        Manga.MangaBuilder manga = Manga.builder();

        manga.yearOfRelease( request.getYearOfRelease() );
        manga.status( request.getStatus() );
        manga.author( request.getAuthor() );
        manga.description( request.getDescription() );
        manga.title( request.getTitle() );

        manga.deleted( false );

        return manga.build();
    }

    @Override
    public MangaResponse toMangaResponse(Manga manga) {
        if ( manga == null ) {
            return null;
        }

        MangaResponse.MangaResponseBuilder mangaResponse = MangaResponse.builder();

        mangaResponse.genres( genresToStringList( manga.getGenres() ) );
        mangaResponse.lastChapterId( manga.getLastChapterId() );
        mangaResponse.author( manga.getAuthor() );
        mangaResponse.comments( manga.getComments() );
        mangaResponse.coverUrl( manga.getCoverUrl() );
        mangaResponse.createdAt( manga.getCreatedAt() );
        mangaResponse.deleted( manga.isDeleted() );
        mangaResponse.deletedAt( manga.getDeletedAt() );
        mangaResponse.deletedBy( manga.getDeletedBy() );
        mangaResponse.description( manga.getDescription() );
        mangaResponse.id( manga.getId() );
        mangaResponse.lastChapterAddedAt( manga.getLastChapterAddedAt() );
        mangaResponse.loves( manga.getLoves() );
        mangaResponse.status( manga.getStatus() );
        mangaResponse.title( manga.getTitle() );
        mangaResponse.updatedAt( manga.getUpdatedAt() );
        mangaResponse.views( manga.getViews() );
        mangaResponse.yearOfRelease( manga.getYearOfRelease() );

        return mangaResponse.build();
    }

    @Override
    public MangaSummaryResponse toMangaSummaryResponse(Manga manga) {
        if ( manga == null ) {
            return null;
        }

        MangaSummaryResponse.MangaSummaryResponseBuilder mangaSummaryResponse = MangaSummaryResponse.builder();

        mangaSummaryResponse.id( manga.getId() );
        mangaSummaryResponse.title( manga.getTitle() );
        mangaSummaryResponse.author( manga.getAuthor() );
        mangaSummaryResponse.coverUrl( manga.getCoverUrl() );
        mangaSummaryResponse.lastChapterId( manga.getLastChapterId() );
        mangaSummaryResponse.lastChapterAddedAt( manga.getLastChapterAddedAt() );
        mangaSummaryResponse.views( manga.getViews() );
        mangaSummaryResponse.loves( manga.getLoves() );
        mangaSummaryResponse.comments( manga.getComments() );
        mangaSummaryResponse.createdAt( manga.getCreatedAt() );
        mangaSummaryResponse.status( manga.getStatus() );
        mangaSummaryResponse.updatedAt( manga.getUpdatedAt() );

        return mangaSummaryResponse.build();
    }

    @Override
    public void updateManga(Manga manga, MangaRequest request) {
        if ( request == null ) {
            return;
        }

        manga.setAuthor( request.getAuthor() );
        manga.setDescription( request.getDescription() );
        manga.setStatus( request.getStatus() );
        manga.setTitle( request.getTitle() );
        manga.setYearOfRelease( request.getYearOfRelease() );
    }

    @Override
    public MangaManagementResponse toMangaManagementResponse(Manga manga) {
        if ( manga == null ) {
            return null;
        }

        MangaManagementResponse.MangaManagementResponseBuilder mangaManagementResponse = MangaManagementResponse.builder();

        mangaManagementResponse.genres( genresToStringList( manga.getGenres() ) );
        mangaManagementResponse.author( manga.getAuthor() );
        mangaManagementResponse.comments( manga.getComments() );
        mangaManagementResponse.coverUrl( manga.getCoverUrl() );
        mangaManagementResponse.createdAt( manga.getCreatedAt() );
        mangaManagementResponse.deleted( manga.isDeleted() );
        mangaManagementResponse.deletedAt( manga.getDeletedAt() );
        mangaManagementResponse.deletedBy( manga.getDeletedBy() );
        mangaManagementResponse.description( manga.getDescription() );
        mangaManagementResponse.id( manga.getId() );
        mangaManagementResponse.lastChapterAddedAt( manga.getLastChapterAddedAt() );
        mangaManagementResponse.lastChapterId( manga.getLastChapterId() );
        mangaManagementResponse.loves( manga.getLoves() );
        mangaManagementResponse.status( manga.getStatus() );
        mangaManagementResponse.title( manga.getTitle() );
        mangaManagementResponse.updatedAt( manga.getUpdatedAt() );
        mangaManagementResponse.views( manga.getViews() );
        mangaManagementResponse.yearOfRelease( manga.getYearOfRelease() );

        return mangaManagementResponse.build();
    }
}
