package com.raindrop.manga_service.mapper;

import com.raindrop.manga_service.dto.response.ChapterResponse;
import com.raindrop.manga_service.dto.response.PageResponse;
import com.raindrop.manga_service.entity.Chapter;
import com.raindrop.manga_service.entity.Manga;
import com.raindrop.manga_service.entity.Page;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-01T10:48:35+0700",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ChapterMapperImpl implements ChapterMapper {

    @Override
    public ChapterResponse toChapterResponse(Chapter chapter) {
        if ( chapter == null ) {
            return null;
        }

        ChapterResponse.ChapterResponseBuilder chapterResponse = ChapterResponse.builder();

        chapterResponse.id( chapter.getId() );
        chapterResponse.views( chapter.getViews() );
        chapterResponse.comments( chapter.getComments() );
        chapterResponse.mangaId( chapterMangaId( chapter ) );
        chapterResponse.chapterNumber( chapter.getChapterNumber() );
        chapterResponse.pages( pageListToPageResponseList( chapter.getPages() ) );
        chapterResponse.title( chapter.getTitle() );
        chapterResponse.updatedAt( chapter.getUpdatedAt() );

        return chapterResponse.build();
    }

    private String chapterMangaId(Chapter chapter) {
        if ( chapter == null ) {
            return null;
        }
        Manga manga = chapter.getManga();
        if ( manga == null ) {
            return null;
        }
        String id = manga.getId();
        if ( id == null ) {
            return null;
        }
        return id;
    }

    protected PageResponse pageToPageResponse(Page page) {
        if ( page == null ) {
            return null;
        }

        PageResponse.PageResponseBuilder pageResponse = PageResponse.builder();

        pageResponse.index( page.getIndex() );
        pageResponse.pageUrl( page.getPageUrl() );

        return pageResponse.build();
    }

    protected List<PageResponse> pageListToPageResponseList(List<Page> list) {
        if ( list == null ) {
            return null;
        }

        List<PageResponse> list1 = new ArrayList<PageResponse>( list.size() );
        for ( Page page : list ) {
            list1.add( pageToPageResponse( page ) );
        }

        return list1;
    }
}
